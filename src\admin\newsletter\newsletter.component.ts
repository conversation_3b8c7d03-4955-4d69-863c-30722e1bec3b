import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AccountService } from '../../Utils/_services/account.service';
import { PageTitleService } from '../../Utils/_services/page-title.service';

@Component({
  selector: 'app-newsletter',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './newsletter.component.html',
  styleUrl: './newsletter.component.css'
})
export class NewsletterComponent implements OnInit {
  subscribers: any[] = [];
  allSubscribers: any[] = [];
  totalCount: number = 0;
  pageNumber: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  isLoading: boolean = false;
  errorMessage: string = '';

  constructor(
    private accountService: AccountService,
    private pageTitleService: PageTitleService
  ) {}

  ngOnInit(): void {
    this.pageTitleService.setTitle('Newsletter Subscribers');
    this.loadAllSubscribers();
  }

  loadAllSubscribers(): void {
    this.isLoading = true;
    this.errorMessage = '';

    // Fetch all users with a large page size to get all newsletter subscribers
    this.accountService.GetAllUser(1, 1000).subscribe({
      next: (response) => {
        // Filter for newsletter subscribers
        this.allSubscribers = (response.items || []).filter((user: any) => user.newsletter === true);
        this.totalCount = this.allSubscribers.length;
        this.totalPages = Math.ceil(this.totalCount / this.pageSize);
        this.updatePaginatedSubscribers();
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Failed to fetch newsletter subscribers', err);
        this.errorMessage = 'Failed to load newsletter subscribers. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  updatePaginatedSubscribers(): void {
    const startIndex = (this.pageNumber - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.subscribers = this.allSubscribers.slice(startIndex, endIndex);
  }

  nextPage(): void {
    if (this.pageNumber < this.totalPages) {
      this.pageNumber++;
      this.updatePaginatedSubscribers();
    }
  }

  prevPage(): void {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.updatePaginatedSubscribers();
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.pageNumber) {
      this.pageNumber = page;
      this.updatePaginatedSubscribers();
    }
  }

  onPageSizeChange(): void {
    this.pageNumber = 1;
    this.totalPages = Math.ceil(this.totalCount / this.pageSize);
    this.updatePaginatedSubscribers();
  }

  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.pageNumber - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getStartIndex(): number {
    return (this.pageNumber - 1) * this.pageSize + 1;
  }

  getEndIndex(): number {
    return Math.min(this.pageNumber * this.pageSize, this.totalCount);
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString();
  }
}
